import { Request, Response } from 'express';
import { FavoriteService } from '../services/favorite.service';
import { NotFoundError } from '../utils/errors';
import { JwtPayload } from '../utils/token.utils';

interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

export class FavoriteController {
  /**
   * Get user's favorite games
   * GET /favorites
   */
  static async getFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;

      const favorites = await FavoriteService.getUserFavorites(userId);

      res.status(200).json({
        status: 'success',
        message: 'Favorites retrieved successfully',
        data: {
          favorites,
          count: favorites.length,
        },
      });
    } catch (error) {
      console.error('Error in getFavorites:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to retrieve favorites',
      });
    }
  }

  /**
   * Add a game to favorites
   * POST /add-to-favorites
   */
  static async addToFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { gameId } = req.body;

      // Validate gameId
      if (!gameId) {
        res.status(400).json({
          status: 'error',
          message: 'Game ID is required',
        });
        return;
      }

      if (typeof gameId !== 'string' || gameId.trim().length === 0) {
        res.status(400).json({
          status: 'error',
          message: 'Game ID must be a valid string',
        });
        return;
      }

      const result = await FavoriteService.addToFavorites(userId, gameId.trim());

      if (result.success) {
        res.status(201).json({
          status: 'success',
          message: result.message,
          data: {
            gameId: gameId.trim(),
            userId,
          },
        });
      } else {
        res.status(409).json({
          status: 'error',
          message: result.message,
        });
      }
    } catch (error) {
      console.error('Error in addToFavorites:', error);
      
      if (error instanceof NotFoundError) {
        res.status(404).json({
          status: 'error',
          message: error.message,
        });
        return;
      }

      res.status(500).json({
        status: 'error',
        message: 'Failed to add game to favorites',
      });
    }
  }

  /**
   * Remove a game from favorites
   * DELETE /delete-from-favorites
   */
  static async removeFromFavorites(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { gameId } = req.body;

      // Validate gameId
      if (!gameId) {
        res.status(400).json({
          status: 'error',
          message: 'Game ID is required',
        });
        return;
      }

      if (typeof gameId !== 'string' || gameId.trim().length === 0) {
        res.status(400).json({
          status: 'error',
          message: 'Game ID must be a valid string',
        });
        return;
      }

      const result = await FavoriteService.removeFromFavorites(userId, gameId.trim());

      if (result.success) {
        res.status(200).json({
          status: 'success',
          message: result.message,
          data: {
            gameId: gameId.trim(),
            userId,
          },
        });
      } else {
        res.status(404).json({
          status: 'error',
          message: result.message,
        });
      }
    } catch (error) {
      console.error('Error in removeFromFavorites:', error);
      
      if (error instanceof NotFoundError) {
        res.status(404).json({
          status: 'error',
          message: error.message,
        });
        return;
      }

      res.status(500).json({
        status: 'error',
        message: 'Failed to remove game from favorites',
      });
    }
  }

  /**
   * Check if a game is in user's favorites
   * GET /favorites/check/:gameId
   */
  static async checkFavorite(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;
      const { gameId } = req.params;

      // Validate gameId
      if (!gameId || typeof gameId !== 'string' || gameId.trim().length === 0) {
        res.status(400).json({
          status: 'error',
          message: 'Valid Game ID is required',
        });
        return;
      }

      const isFavorite = await FavoriteService.isGameInFavorites(userId, gameId.trim());

      res.status(200).json({
        status: 'success',
        data: {
          gameId: gameId.trim(),
          isFavorite,
        },
      });
    } catch (error) {
      console.error('Error in checkFavorite:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to check favorite status',
      });
    }
  }

  /**
   * Get favorites count for user
   * GET /favorites/count
   */
  static async getFavoritesCount(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user!.id;

      const count = await FavoriteService.getFavoritesCount(userId);

      res.status(200).json({
        status: 'success',
        data: {
          count,
        },
      });
    } catch (error) {
      console.error('Error in getFavoritesCount:', error);
      res.status(500).json({
        status: 'error',
        message: 'Failed to get favorites count',
      });
    }
  }
}
