import Favorite from '../models/favorite.model';
import Game from '../models/game.model';
import User from '../models/user.model';
import { redisService } from './redis.service';
import { NotFoundError } from '../utils/errors';
import { Op } from 'sequelize';

export class FavoriteService {
  /**
   * Get user's favorite games with full game details
   */
  static async getUserFavorites(userId: number): Promise<any[]> {
    try {
      // Check Redis cache first
      const cachedFavorites = await redisService.getFavorites(userId);
      if (cachedFavorites) {
        return cachedFavorites;
      }

      // If not in cache, fetch from database
      const favorites = await Favorite.findAll({
        where: { user_id: userId },
        include: [
          {
            model: Game,
            as: 'game',
            attributes: ['id', 'name', 'title', 'img', 'device', 'categories', 'flash'],
          },
        ],
        order: [['created_at', 'DESC']],
      });

      // Extract game data from favorites
      const favoriteGames = favorites.map(favorite => favorite.game);

      // Cache the result
      await redisService.setFavorites(userId, favoriteGames);

      return favoriteGames;
    } catch (error) {
      console.error('Error getting user favorites:', error);
      throw error;
    }
  }

  /**
   * Add a game to user's favorites
   */
  static async addToFavorites(userId: number, gameId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify user exists and is active
      const user = await User.findByPk(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }
      if (!user.is_active || user.is_banned) {
        throw new Error('User account is not active');
      }

      // Verify game exists
      const game = await Game.findByPk(gameId);
      if (!game) {
        throw new NotFoundError('Game not found');
      }

      // Check if already in favorites
      const existingFavorite = await Favorite.findOne({
        where: {
          user_id: userId,
          game_id: gameId,
        },
      });

      if (existingFavorite) {
        return {
          success: false,
          message: 'Game is already in favorites',
        };
      }

      // Add to favorites
      await Favorite.create({
        user_id: userId,
        game_id: gameId,
      });

      // Invalidate cache
      await redisService.deleteFavorites(userId);

      return {
        success: true,
        message: 'Game added to favorites successfully',
      };
    } catch (error) {
      console.error('Error adding to favorites:', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error('Failed to add game to favorites');
    }
  }

  /**
   * Remove a game from user's favorites
   */
  static async removeFromFavorites(userId: number, gameId: string): Promise<{ success: boolean; message: string }> {
    try {
      // Verify user exists and is active
      const user = await User.findByPk(userId);
      if (!user) {
        throw new NotFoundError('User not found');
      }
      if (!user.is_active || user.is_banned) {
        throw new Error('User account is not active');
      }

      // Find and remove the favorite
      const favorite = await Favorite.findOne({
        where: {
          user_id: userId,
          game_id: gameId,
        },
      });

      if (!favorite) {
        return {
          success: false,
          message: 'Game is not in favorites',
        };
      }

      await favorite.destroy();

      // Invalidate cache
      await redisService.deleteFavorites(userId);

      return {
        success: true,
        message: 'Game removed from favorites successfully',
      };
    } catch (error) {
      console.error('Error removing from favorites:', error);
      if (error instanceof NotFoundError) {
        throw error;
      }
      throw new Error('Failed to remove game from favorites');
    }
  }

  /**
   * Check if a game is in user's favorites
   */
  static async isGameInFavorites(userId: number, gameId: string): Promise<boolean> {
    try {
      const favorite = await Favorite.findOne({
        where: {
          user_id: userId,
          game_id: gameId,
        },
      });

      return !!favorite;
    } catch (error) {
      console.error('Error checking if game is in favorites:', error);
      return false;
    }
  }

  /**
   * Get favorites count for a user
   */
  static async getFavoritesCount(userId: number): Promise<number> {
    try {
      const count = await Favorite.count({
        where: { user_id: userId },
      });

      return count;
    } catch (error) {
      console.error('Error getting favorites count:', error);
      return 0;
    }
  }

  /**
   * Get users who favorited a specific game
   */
  static async getUsersWhoFavoritedGame(gameId: string): Promise<number[]> {
    try {
      const favorites = await Favorite.findAll({
        where: { game_id: gameId },
        attributes: ['user_id'],
      });

      return favorites.map(favorite => favorite.user_id);
    } catch (error) {
      console.error('Error getting users who favorited game:', error);
      return [];
    }
  }
}
