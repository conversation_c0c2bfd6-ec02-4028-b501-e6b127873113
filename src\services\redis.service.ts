import Redis from 'ioredis';
import { logger } from '../utils/logger';

class RedisService {
  private client: Redis;
  private readonly GAME_PREFIX = 'game:';
  private readonly SEARCH_PREFIX = 'search:';
  private readonly CATEGORY_PREFIX = 'category:';
  private readonly TITLE_PREFIX = 'title:';
  private readonly FAVORITES_PREFIX = 'favorites:';
  private readonly ALL_GAMES_KEY = 'games:all';
  private readonly EXPIRY_TIME = 60 * 60 * 24 * 7; // 7 days in seconds
  private readonly FAVORITES_EXPIRY_TIME = 60 * 60; // 1 hour in seconds

  constructor() {
    this.client = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: Number(process.env.REDIS_PORT) || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
      db: Number(process.env.REDIS_DB) || 0,
      retryStrategy: (times) => Math.min(times * 50, 2000),
      lazyConnect: true, // Don't connect immediately
      maxRetriesPerRequest: 3
    });

    this.client.on('error', (err) => {
      logger.error('Redis connection error:', err);
    });

    this.client.on('connect', () => {
      logger.info('Connected to Redis');
    });

    this.client.on('ready', () => {
      logger.info('Redis client ready');
    });

    this.client.on('close', () => {
      logger.warn('Redis connection closed');
    });
  }

  // Game caching methods
  async getGame(gameId: string): Promise<any | null> {
    try {
      const gameData = await this.client.get(`${this.GAME_PREFIX}${gameId}`);
      return gameData ? JSON.parse(gameData) : null;
    } catch (error) {
      logger.error('Redis getGame error:', error);
      return null;
    }
  }

  async setGame(gameId: string, gameData: any): Promise<boolean> {
    try {
      await this.client.set(
        `${this.GAME_PREFIX}${gameId}`,
        JSON.stringify(gameData),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setGame error:', error);
      return false;
    }
  }

  // Cache all games
  async setAllGames(games: any[]): Promise<boolean> {
    try {
      await this.client.set(
        this.ALL_GAMES_KEY,
        JSON.stringify(games),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setAllGames error:', error);
      return false;
    }
  }

  async getAllGames(): Promise<any[] | null> {
    try {
      const gamesData = await this.client.get(this.ALL_GAMES_KEY);
      return gamesData ? JSON.parse(gamesData) : null;
    } catch (error) {
      logger.error('Redis getAllGames error:', error);
      return null;
    }
  }

  // Search results caching
  async getSearchResults(searchParams: string): Promise<any | null> {
    try {
      const key = `${this.SEARCH_PREFIX}${this.hashSearchParams(searchParams)}`;
      const data = await this.client.get(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getSearchResults error:', error);
      return null;
    }
  }

  async setSearchResults(searchParams: string, results: any): Promise<boolean> {
    try {
      const key = `${this.SEARCH_PREFIX}${this.hashSearchParams(searchParams)}`;
      await this.client.set(key, JSON.stringify(results), 'EX', 3600); // 1 hour expiry for search results
      return true;
    } catch (error) {
      logger.error('Redis setSearchResults error:', error);
      return false;
    }
  }

  // Categories and titles caching
  async setCategories(categories: string[]): Promise<boolean> {
    try {
      await this.client.set(
        this.CATEGORY_PREFIX + 'all',
        JSON.stringify(categories),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setCategories error:', error);
      return false;
    }
  }

  async getCategories(): Promise<string[] | null> {
    try {
      const data = await this.client.get(this.CATEGORY_PREFIX + 'all');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getCategories error:', error);
      return null;
    }
  }

  async setTitles(titles: string[]): Promise<boolean> {
    try {
      await this.client.set(
        this.TITLE_PREFIX + 'all',
        JSON.stringify(titles),
        'EX',
        this.EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setTitles error:', error);
      return false;
    }
  }

  async getTitles(): Promise<string[] | null> {
    try {
      const data = await this.client.get(this.TITLE_PREFIX + 'all');
      return data ? JSON.parse(data) : null;
    } catch (error) {
      logger.error('Redis getTitles error:', error);
      return null;
    }
  }

  // Favorites caching methods
  async getFavorites(userId: number): Promise<any[] | null> {
    try {
      const favoritesData = await this.client.get(`${this.FAVORITES_PREFIX}${userId}`);
      return favoritesData ? JSON.parse(favoritesData) : null;
    } catch (error) {
      logger.error('Redis getFavorites error:', error);
      return null;
    }
  }

  async setFavorites(userId: number, favorites: any[]): Promise<boolean> {
    try {
      await this.client.set(
        `${this.FAVORITES_PREFIX}${userId}`,
        JSON.stringify(favorites),
        'EX',
        this.FAVORITES_EXPIRY_TIME
      );
      return true;
    } catch (error) {
      logger.error('Redis setFavorites error:', error);
      return false;
    }
  }

  async deleteFavorites(userId: number): Promise<boolean> {
    try {
      await this.client.del(`${this.FAVORITES_PREFIX}${userId}`);
      return true;
    } catch (error) {
      logger.error('Redis deleteFavorites error:', error);
      return false;
    }
  }

  // Cache invalidation
  async invalidateGameCache(): Promise<void> {
    try {
      // Get all keys with game prefix
      const keys = await this.client.keys(`${this.GAME_PREFIX}*`);
      if (keys.length > 0) {
        await this.client.del(...keys);
      }

      // Delete all games key
      await this.client.del(this.ALL_GAMES_KEY);

      // Delete categories and titles
      await this.client.del(this.CATEGORY_PREFIX + 'all');
      await this.client.del(this.TITLE_PREFIX + 'all');

      // Delete search results
      const searchKeys = await this.client.keys(`${this.SEARCH_PREFIX}*`);
      if (searchKeys.length > 0) {
        await this.client.del(...searchKeys);
      }

      logger.info('Game cache invalidated');
    } catch (error) {
      logger.error('Redis invalidateGameCache error:', error);
    }
  }

  // Helper method to create a consistent hash for search parameters
  private hashSearchParams(searchParams: string): string {
    return Buffer.from(searchParams).toString('base64');
  }

  // Health check method
  async isConnected(): Promise<boolean> {
    try {
      await this.client.ping();
      return true;
    } catch (error) {
      logger.error('Redis health check failed:', error);
      return false;
    }
  }

  // Graceful shutdown
  async disconnect(): Promise<void> {
    try {
      await this.client.quit();
      logger.info('Redis client disconnected gracefully');
    } catch (error) {
      logger.error('Error disconnecting Redis client:', error);
    }
  }
}

export const redisService = new RedisService();
