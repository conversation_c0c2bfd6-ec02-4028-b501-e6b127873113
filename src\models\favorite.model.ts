import { DataTypes, Model } from 'sequelize';
import sequelize from '../config/database';
import User from './user.model';
import Game from './game.model';

export interface FavoriteAttributes {
  id?: number;
  user_id: number;
  game_id: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface FavoriteCreationAttributes extends Omit<FavoriteAttributes, 'id'> {}

class Favorite extends Model<FavoriteAttributes, FavoriteCreationAttributes> implements FavoriteAttributes {
  public id!: number;
  public user_id!: number;
  public game_id!: string;
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Association properties
  public user?: User;
  public game?: Game;
}

Favorite.init(
  {
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      autoIncrement: true,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
    game_id: {
      type: DataTypes.STRING,
      allowNull: false,
      references: {
        model: 'games',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    sequelize,
    modelName: 'Favorite',
    tableName: 'favorites',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['user_id', 'game_id'],
        name: 'unique_user_game_favorite',
      },
      {
        fields: ['user_id'],
        name: 'idx_favorites_user_id',
      },
      {
        fields: ['game_id'],
        name: 'idx_favorites_game_id',
      },
    ],
  }
);

// Define associations
Favorite.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

Favorite.belongsTo(Game, {
  foreignKey: 'game_id',
  as: 'game',
});

// Add reverse associations
User.hasMany(Favorite, {
  foreignKey: 'user_id',
  as: 'favorites',
});

Game.hasMany(Favorite, {
  foreignKey: 'game_id',
  as: 'favorites',
});

export default Favorite;
