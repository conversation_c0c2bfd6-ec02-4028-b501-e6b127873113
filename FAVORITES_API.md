# Favorites API Documentation

## Overview

The Favorites API allows authenticated players to manage their favorite games. Players can add games to their favorites list, remove games from favorites, and retrieve their complete favorites list. All endpoints require player authentication and use Redis caching for optimal performance.

## Base URL

```
http://localhost:3000/api/v1/games
```

## Authentication

All favorites endpoints require:
- **Authentication**: Valid JWT access token in HTTP-only cookie
- **Authorization**: User must have `PLAYER` role
- **CSRF Protection**: Valid CSRF token for POST/DELETE operations

## Endpoints

### 1. Get User's Favorite Games

Retrieve the complete list of games that the authenticated player has added to their favorites.

**Endpoint:** `GET /favorites`

**Headers:**
```
Cookie: accessToken=<jwt_token>; csrf_token_personal=<csrf_token>
```

**Response:**

**Success (200 OK):**
```json
{
  "status": "success",
  "message": "Favorites retrieved successfully",
  "data": {
    "favorites": [
      {
        "id": "game123",
        "name": "Slot Game 1",
        "title": "Amazing Slots",
        "img": "https://example.com/game1.jpg",
        "device": "desktop",
        "categories": "slots,classic",
        "flash": false
      },
      {
        "id": "game456",
        "name": "Poker Game",
        "title": "Texas Hold'em",
        "img": "https://example.com/game2.jpg",
        "device": "mobile",
        "categories": "poker,cards",
        "flash": false
      }
    ],
    "count": 2
  }
}
```

**Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to retrieve favorites"
}
```

### 2. Add Game to Favorites

Add a specific game to the authenticated player's favorites list.

**Endpoint:** `POST /add-to-favorites`

**Headers:**
```
Content-Type: application/json
Cookie: accessToken=<jwt_token>; csrf_token_personal=<csrf_token>
X-CSRF-Token: <csrf_token>
```

**Request Body:**
```json
{
  "gameId": "game123"
}
```

**Response:**

**Success (201 Created):**
```json
{
  "status": "success",
  "message": "Game added to favorites successfully",
  "data": {
    "gameId": "game123",
    "userId": 12345
  }
}
```

**Already in Favorites (409 Conflict):**
```json
{
  "status": "error",
  "message": "Game is already in favorites"
}
```

**Game Not Found (404 Not Found):**
```json
{
  "status": "error",
  "message": "Game not found"
}
```

**Validation Error (400 Bad Request):**
```json
{
  "status": "error",
  "message": "Game ID is required"
}
```

**Server Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to add game to favorites"
}
```

### 3. Remove Game from Favorites

Remove a specific game from the authenticated player's favorites list.

**Endpoint:** `DELETE /delete-from-favorites`

**Headers:**
```
Content-Type: application/json
Cookie: accessToken=<jwt_token>; csrf_token_personal=<csrf_token>
X-CSRF-Token: <csrf_token>
```

**Request Body:**
```json
{
  "gameId": "game123"
}
```

**Response:**

**Success (200 OK):**
```json
{
  "status": "success",
  "message": "Game removed from favorites successfully",
  "data": {
    "gameId": "game123",
    "userId": 12345
  }
}
```

**Not in Favorites (404 Not Found):**
```json
{
  "status": "error",
  "message": "Game is not in favorites"
}
```

**Validation Error (400 Bad Request):**
```json
{
  "status": "error",
  "message": "Game ID is required"
}
```

**Server Error (500 Internal Server Error):**
```json
{
  "status": "error",
  "message": "Failed to remove game from favorites"
}
```

## Usage Examples

### JavaScript/Fetch API

#### Get Favorites
```javascript
const getFavorites = async () => {
  try {
    const response = await fetch('/api/v1/games/favorites', {
      method: 'GET',
      credentials: 'include', // Include cookies
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('User favorites:', data.data.favorites);
      console.log('Total count:', data.data.count);
    }
  } catch (error) {
    console.error('Error fetching favorites:', error);
  }
};
```

#### Add to Favorites
```javascript
const addToFavorites = async (gameId, csrfToken) => {
  try {
    const response = await fetch('/api/v1/games/add-to-favorites', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      },
      credentials: 'include',
      body: JSON.stringify({ gameId })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('Game added to favorites successfully');
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Error adding to favorites:', error);
  }
};
```

#### Remove from Favorites
```javascript
const removeFromFavorites = async (gameId, csrfToken) => {
  try {
    const response = await fetch('/api/v1/games/delete-from-favorites', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken
      },
      credentials: 'include',
      body: JSON.stringify({ gameId })
    });
    
    const data = await response.json();
    
    if (data.status === 'success') {
      console.log('Game removed from favorites successfully');
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Error removing from favorites:', error);
  }
};
```

### cURL Examples

#### Get Favorites
```bash
curl -X GET \
  http://localhost:3000/api/v1/games/favorites \
  -H "Cookie: accessToken=your_jwt_token; csrf_token_personal=your_csrf_token"
```

#### Add to Favorites
```bash
curl -X POST \
  http://localhost:3000/api/v1/games/add-to-favorites \
  -H "Content-Type: application/json" \
  -H "Cookie: accessToken=your_jwt_token; csrf_token_personal=your_csrf_token" \
  -H "X-CSRF-Token: your_csrf_token" \
  -d '{"gameId": "game123"}'
```

#### Remove from Favorites
```bash
curl -X DELETE \
  http://localhost:3000/api/v1/games/delete-from-favorites \
  -H "Content-Type: application/json" \
  -H "Cookie: accessToken=your_jwt_token; csrf_token_personal=your_csrf_token" \
  -H "X-CSRF-Token: your_csrf_token" \
  -d '{"gameId": "game123"}'
```

## Error Handling

### Common Error Responses

**Unauthorized (401):**
```json
{
  "status": "error",
  "message": "Authentication required"
}
```

**Forbidden (403):**
```json
{
  "status": "error",
  "message": "Insufficient permissions. Player role required."
}
```

**CSRF Token Missing (403):**
```json
{
  "status": "error",
  "message": "CSRF token required"
}
```

**Rate Limit Exceeded (429):**
```json
{
  "status": "error",
  "message": "Too many requests. Please try again later."
}
```

## Caching

- **Redis Caching**: Favorites lists are cached in Redis with a 1-hour expiration
- **Cache Key Pattern**: `favorites:{userId}`
- **Cache Invalidation**: Cache is automatically cleared when favorites are added or removed
- **Fallback**: If Redis is unavailable, data is fetched directly from the database

## Database Schema

The favorites functionality uses a `favorites` table with the following structure:

```sql
CREATE TABLE favorites (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  user_id BIGINT UNSIGNED NOT NULL,
  game_id VARCHAR(255) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user_game_favorite (user_id, game_id),
  KEY idx_favorites_user_id (user_id),
  KEY idx_favorites_game_id (game_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (game_id) REFERENCES games(id) ON DELETE CASCADE
);
```

## Security Considerations

1. **Authentication Required**: All endpoints require valid JWT tokens
2. **Role-Based Access**: Only users with `PLAYER` role can access these endpoints
3. **CSRF Protection**: POST and DELETE operations require valid CSRF tokens
4. **Input Validation**: All inputs are validated and sanitized
5. **Rate Limiting**: Endpoints are protected by rate limiting middleware
6. **SQL Injection Prevention**: Using Sequelize ORM with parameterized queries

## Performance Features

1. **Redis Caching**: Frequently accessed favorites lists are cached
2. **Database Indexing**: Optimized database queries with proper indexes
3. **Unique Constraints**: Prevents duplicate favorites at the database level
4. **Efficient Queries**: Uses JOIN operations to fetch complete game details

## Notes

- Game IDs must exist in the games table before they can be added to favorites
- Users can only manage their own favorites (enforced by authentication)
- The system prevents duplicate favorites through database constraints
- All timestamps are automatically managed by the database
- Cache is automatically invalidated when favorites are modified to ensure data consistency
